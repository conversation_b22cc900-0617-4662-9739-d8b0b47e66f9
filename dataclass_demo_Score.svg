<svg class="railroad-diagram" height="196" viewBox="0 0 386 196" width="386" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g transform="translate(.5 .5)">
<g>
<path class="start" d="M20 69v20m10 -20v20m-10 -10h20" /></g><path d="M40 79h10" /><g>
<path class="group gr1" d="M50 79h0.0" /><path class="group gr2" d="M336.0 149h0.0" /><rect class="group-box" height="140" rx="10" ry="10" width="286" x="50" y="36"></rect><g>
<path class="stack stack1" d="M50.0 79h10.0" /><path class="stack stack2" d="M60.0 79h10" /><g>
<path class="group gr1" d="M70.0 79h10.0" /><path class="group gr2" d="M306.0 79h10.0" /><rect class="group-box" height="38" rx="10" ry="10" width="226" x="80" y="60"></rect><g>
<path class="seq seq1" d="M80.0 79h10.0" /><path class="seq seq2" d="M296.0 79h10.0" /><g>
<path class="seq seq1" d="M90.0 79h0.0" /><path class="seq seq2" d="M220.0 79h0.0" /><g class="non-terminal ">
<path class="nonterm nt1" d="M90.0 79h0.0" /><path class="nonterm nt2" d="M134.0 79h0.0" /><rect height="22" width="44" x="90" y="68"></rect><text x="112" y="83">\d+</text></g><path class="seq seq4" d="M134.0 79h10" /><path class="seq seq3" d="M144.0 79h10" /><g class="non-terminal ">
<path class="comment com1" d="M154.0 79h0.0" /><path class="comment com2" d="M220.0 79h0.0" /><text class="comment" x="187" y="84">map: int</text></g></g><path class="seq seq4" d="M220.0 79h10" /><path class="seq seq3" d="M230.0 79h10" /><g>
<path class="stack stack1" d="M240.0 79h0.0" /><g class="terminal ">
<path class="terminal term1" d="M240.0 79h10.0" /><path class="terminal term2" d="M286.0 79h10.0" /><rect height="22" rx="10" ry="10" width="36" x="250" y="68"></rect><text x="268" y="83">, </text></g><path class="stack stack5" d="M296.0 79h0.0" /></g></g><g class="non-terminal ">
<path class="comment com1" d="M80.0 52h0.0" /><path class="comment com2" d="M174.0 52h0.0" /><text class="comment" x="127" y="57">field:number</text></g></g><path class="stack stack3" d="M316.0 79a10 10 0 0 1 10 10v7a10 10 0 0 1 -10 10h-246a10 10 0 0 0 -10 10v23a10 10 0 0 0 10 10h10" /><g>
<path class="group gr1" d="M70.0 149h10.0" /><path class="group gr2" d="M306.0 149h10.0" /><rect class="group-box" height="38" rx="10" ry="10" width="226" x="80" y="130"></rect><g>
<path class="seq seq1" d="M80.0 149h10.0" /><path class="seq seq2" d="M296.0 149h10.0" /><g>
<path class="seq seq1" d="M90.0 149h0.0" /><path class="seq seq2" d="M220.0 149h0.0" /><g class="non-terminal ">
<path class="nonterm nt1" d="M90.0 149h0.0" /><path class="nonterm nt2" d="M134.0 149h0.0" /><rect height="22" width="44" x="90" y="138"></rect><text x="112" y="153">\d+</text></g><path class="seq seq4" d="M134.0 149h10" /><path class="seq seq3" d="M144.0 149h10" /><g class="non-terminal ">
<path class="comment com1" d="M154.0 149h0.0" /><path class="comment com2" d="M220.0 149h0.0" /><text class="comment" x="187" y="154">map: int</text></g></g><path class="seq seq4" d="M220.0 149h10" /><path class="seq seq3" d="M230.0 149h10" /><g>
<path class="stack stack1" d="M240.0 149h0.0" /><g class="terminal ">
<path class="terminal term1" d="M240.0 149h10.0" /><path class="terminal term2" d="M286.0 149h10.0" /><rect height="22" rx="10" ry="10" width="36" x="250" y="138"></rect><text x="268" y="153">\n</text></g><path class="stack stack5" d="M296.0 149h0.0" /></g></g><g class="non-terminal ">
<path class="comment com1" d="M80.0 122h0.0" /><path class="comment com2" d="M167.0 122h0.0" /><text class="comment" x="123.5" y="127">field:score</text></g></g><path class="stack stack4" d="M316.0 149h10" /><path class="stack stack5" d="M326.0 149h10.0" /></g><g class="non-terminal ">
<path class="comment com1" d="M50.0 28h0.0" /><path class="comment com2" d="M95.0 28h0.0" /><text class="comment" x="72.5" y="33">Score</text></g></g><path d="M336 149h10" /><path class="end" d="M 346 149 h 20 m -10 -10 v 20 m 10 -20 v 20"></path></g><style>/* <![CDATA[ */
svg.railroad-diagram {
    background-color:hsl(30,20%,95%);
}
svg.railroad-diagram path {
    stroke-width:3;
    stroke:black;
    fill:rgba(0,0,0,0);
}
svg.railroad-diagram path.start {
    stroke-width:3;
    stroke:black;
    fill:rgba(0,0,0,1);
}
svg.railroad-diagram polygon {
    stroke-width:3;
    stroke:black;
    fill:hsl(120,100%,90%);
}
svg.railroad-diagram text {
    font:bold 14px monospace;
    text-anchor:middle;
}
svg.railroad-diagram text.label{
    text-anchor:start;
}
svg.railroad-diagram text.comment{
    font:italic 12px monospace;
}
svg.railroad-diagram rect{
    stroke-width:3;
    stroke:black;
    fill:hsl(120,100%,90%);
}
svg.railroad-diagram rect.group-box {
    stroke: gray;
    stroke-dasharray: 10 5;
    fill: none;
}
/* ]]> */
</style></svg>