"""
Railroad diagram generation for parmancer parsers using pyrailroad.

pyrailroad requires Python >=3.10 and is available as an optional dependency:
    pip install 'parmancer[diagrams]'
"""

from __future__ import annotations

from pathlib import Path
from typing import Any, Dict, Iterable, List, Set

try:
    from pyrailroad.elements import (
        Choice as RChoice,
        Comment,
        Diagram,
        Group,
        NonTerminal,
        OneOrMore,
        Sequence as RSequence,
        Skip,
        Stack,
        Terminal,
        zero_or_more,
    )
    # Type alias for pyrailroad diagram elements
    DiagramElement = Any  # Union of all pyrailroad element types
except ImportError:
    # TODO: pyrailroad is not available (requires Python >=3.10)
    # Add fallback or error handling
    raise ImportError(
        "pyrailroad is required for diagram generation. "
        "Install with: pip install 'parmancer[diagrams]' "
        "(requires Python >=3.10)"
    )
    DiagramElement = Any

from parmancer.parser import (
    Bind,
    Choice,
    DataclassPermutation,
    DataclassSequence,
    EndOfText,
    EnumMember,
    # FailureInfo,  # This exists but is not a parser
    ForwardParser,
    Gate,
    KeepOne,
    LookAhead,
    Map,
    MapFailure,
    OneOf,
    Parser,
    Range,
    Regex,
    Sequence,
    Span,
    StatefulParser,
    String,  # This was LiteralParser
    Success,
    Until,
    gather,
)

_todo = [
    # Failure,  # No longer exists as a parser
    MapFailure,
    Span,
    Bind,
    Gate,
    EndOfText,
    KeepOne,
    Until,
    DataclassSequence,
    DataclassPermutation,
    EnumMember,
    LookAhead,
]


def format_string_escapes(text: str) -> str:
    """Convert newlines, tabs, and other control characters to escape sequences."""
    return text.replace("\n", "\\n").replace("\t", "\\t").replace("\r", "\\r").replace("\v", "\\v").replace("\f", "\\f")


def get_diagram_element(
    parser: Parser[Any], subparsers: Iterable[Parser[Any]] = tuple()
) -> DiagramElement:
    """
    Get the diagram element corresponding to `parser`, recursively filling in
    child parsers.

    When a child parser is in `subparsers`, it will be included by name instead of
    being included in full.
    """
    if any(parser == subparser for subparser in subparsers):
        return NonTerminal(parser.name)
    if isinstance(parser, Sequence):
        return RSequence(
            *(
                get_diagram_element(child, subparsers=subparsers)
                for child in parser.parsers
            )
        )
    if isinstance(parser, (OneOf, Choice)):
        return RChoice(
            0,
            *(
                get_diagram_element(child, subparsers=subparsers)
                for child in parser.parsers
            ),
        )
    if isinstance(parser, Success):
        return Comment(repr(parser.success_value))
        # return Group(Skip(), repr(parser.success_value))
    if isinstance(parser, String):  # LiteralParser was renamed to String
        return Terminal(format_string_escapes(parser.string))
    if isinstance(parser, Regex):
        return NonTerminal(
            parser.pattern.pattern
            + (f" ╎ group={parser.group}" if parser.group != 0 else "")
        )
    if isinstance(parser, Range):
        # TODO the railroad diagram library is broken for separator nodes larger than
        # a single element (the path through it is right-left instead of left-right)
        sep_node = Skip()
        if parser.max_count != float("inf"):
            sep_node = Comment(f"{{{parser.min_count}, {parser.max_count}}}")
        elif parser.min_count != 0:
            sep_node = Comment(f"{{{parser.min_count},}}")

        if parser.separator_parser is not None:
            sep_node = RSequence(
                get_diagram_element(parser.separator_parser), sep_node
            )

        if parser.min_count == 0:
            return zero_or_more(
                get_diagram_element(parser.parser, subparsers=subparsers),
                repeat=sep_node,
            )
        return OneOrMore(
            get_diagram_element(parser.parser, subparsers=subparsers), repeat=sep_node
        )
    if isinstance(parser, KeepOne):
        results: List[DiagramElement] = []
        if parser.left:
            results.append(
                Stack(
                    *(
                        get_diagram_element(child, subparsers=subparsers)
                        for child in parser.left
                    )
                )
            )
        results.append(get_diagram_element(parser.keep, subparsers=subparsers))
        if parser.right:
            results.append(
                Stack(
                    *(
                        get_diagram_element(child, subparsers=subparsers)
                        for child in parser.right
                    )
                )
            )
        return RSequence(*results)
    if isinstance(parser, Map):
        return RSequence(
            get_diagram_element(parser.parser, subparsers=subparsers),
            Comment("map: " + parser.map_name),
        )
    if isinstance(parser, Bind):
        raise ValueError("Bind doesn't work with railroad diagrams")
    if isinstance(parser, DataclassSequence):
        return Group(
            Stack(
                *(
                    Group(
                        get_diagram_element(child, subparsers=subparsers),
                        f"field:{label}",
                    )
                    for label, child in parser.field_parsers.items()
                )
            ),
            parser.name,
        )
    if isinstance(parser, StatefulParser):
        return NonTerminal(parser.name)
    if isinstance(parser, ForwardParser):
        return NonTerminal(parser.get_parser().name)

    # TODO: Add implementations for remaining parsers
    if isinstance(parser, MapFailure):
        # TODO: Implement MapFailure diagram representation
        return Comment(f"MapFailure: {parser.parser.name}")
    if isinstance(parser, Span):
        # TODO: Implement Span diagram representation
        return Terminal(f"Span({parser.length})")
    if isinstance(parser, Gate):
        # TODO: Implement Gate diagram representation
        return RSequence(
            get_diagram_element(parser.parser, subparsers=subparsers),
            Comment(f"gate: {parser.name}")
        )
    if isinstance(parser, EndOfText):
        # TODO: Implement EndOfText diagram representation
        return Terminal("EOF")
    if isinstance(parser, Until):
        # TODO: Implement Until diagram representation
        return Comment(f"Until: {parser.name}")
    if isinstance(parser, DataclassPermutation):
        # TODO: Implement DataclassPermutation diagram representation
        return Group(
            Comment("DataclassPermutation"),
            parser.name,
        )
    if isinstance(parser, EnumMember):
        # TODO: Implement EnumMember diagram representation
        return RChoice(
            0,
            *(Terminal(member.value) for member in parser.enum_class)
        )
    if isinstance(parser, LookAhead):
        # TODO: Implement LookAhead diagram representation
        return RSequence(
            Comment("lookahead"),
            get_diagram_element(parser.parser, subparsers=subparsers)
        )

    raise ValueError(f"Railroad diagram not implemented for: {parser}")


def get_diagram(parser: Parser[Any]) -> Diagram:
    return Diagram(get_diagram_element(parser))


def get_diagrams(*parsers: Parser[Any]) -> Dict[str, Diagram]:
    diagrams: Dict[str, Diagram] = {}
    for parser in parsers:
        diagrams[parser.name] = Diagram(
            get_diagram_element(
                parser, subparsers=tuple(p for p in parsers if p != parser)
            )
        )
    return diagrams


def write_standalone_svg(diagram: Diagram, path: Path) -> None:
    path.unlink(missing_ok=True)
    path.parent.mkdir(parents=True, exist_ok=True)
    path.touch()
    with path.open(mode="w") as f:
        diagram.write_standalone(f.write)


def write_multiple_standalone_svg(
    diagrams: Dict[str, Diagram], base_path: Path
) -> None:
    names: Set[str] = set()
    for name, diagram in diagrams.items():
        while name in names:
            name = name + "_"
        names.add(name)
        path = base_path.with_stem(f"{base_path.stem}_{name}")
        write_standalone_svg(diagram, path)


def write_diagrams_html(diagrams: Dict[str, Diagram], path: Path) -> None:
    """Write multiple diagrams as inline SVGs in a single HTML document."""
    path.unlink(missing_ok=True)
    path.parent.mkdir(parents=True, exist_ok=True)

    html_content = [
        "<!DOCTYPE html>",
        "<html>",
        "<head>",
        "    <meta charset='utf-8'>",
        "    <title>Parser Diagrams</title>",
        "    <style>",
        "        body { font-family: Arial, sans-serif; margin: 40px; }",
        "        h2 { color: #333; border-bottom: 2px solid #ddd; padding-bottom: 10px; }",
        "        .diagram { margin: 20px 0; }",
        "        svg { border: 1px solid #eee; }",
        "    </style>",
        "</head>",
        "<body>",
        "    <h1>Parser Diagrams</h1>",
    ]

    for name, diagram in diagrams.items():
        html_content.append(f"    <h2>{name}</h2>")
        html_content.append("    <div class='diagram'>")

        # Get SVG content from diagram
        svg_lines: List[str] = []
        diagram.write_svg(svg_lines.append)
        svg_content = "".join(svg_lines)

        # Add the SVG content with proper indentation
        for line in svg_content.split('\n'):
            if line.strip():
                html_content.append(f"        {line}")

        html_content.append("    </div>")

    html_content.extend([
        "</body>",
        "</html>"
    ])

    with path.open(mode="w") as f:
        f.write("\n".join(html_content))

if __name__ == "__main__":
    from examples.dataclass_parser_demo import File, School, Student, Score
    diagrams = get_diagrams(gather(File), gather(Student), gather(Score) )
    write_multiple_standalone_svg(diagrams, Path("dataclass_demo.svg"))
