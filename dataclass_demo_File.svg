<svg class="railroad-diagram" height="602" viewBox="0 0 1033 602" width="1033" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g transform="translate(.5 .5)">
<g>
<path class="start" d="M20 69v20m10 -20v20m-10 -10h20" /></g><path d="M40 79h10" /><g>
<path class="group gr1" d="M50 79h0.0" /><path class="group gr2" d="M983.0 498h0.0" /><rect class="group-box" height="546" rx="10" ry="10" width="933" x="50" y="36"></rect><g>
<path class="stack stack1" d="M50.0 79h10.0" /><path class="stack stack2" d="M60.0 79h10" /><g>
<path class="group gr1" d="M70.0 79h346.5" /><path class="group gr2" d="M616.5 79h346.5" /><rect class="group-box" height="38" rx="10" ry="10" width="200" x="416.5" y="60"></rect><g class="non-terminal ">
<path class="nonterm nt1" d="M416.5 79h10.0" /><path class="nonterm nt2" d="M606.5 79h10.0" /><rect height="22" width="180" x="426.5" y="68"></rect><text x="516.5" y="83">[\s\S]*?(?=School =)</text></g><g class="non-terminal ">
<path class="comment com1" d="M416.5 52h0.0" /><path class="comment com2" d="M510.5 52h0.0" /><text class="comment" x="463.5" y="57">field:header</text></g></g><path class="stack stack3" d="M963.0 79a10 10 0 0 1 10 10v7a10 10 0 0 1 -10 10h-893a10 10 0 0 0 -10 10v79a10 10 0 0 0 10 10h10" /><g>
<path class="group gr1" d="M70.0 205h10.0" /><path class="group gr2" d="M953.0 498h10.0" /><rect class="group-box" height="444" rx="10" ry="10" width="873" x="80" y="130"></rect><g>
<path class="choice ch1" d="M80.0 205h0.0" /><path class="choice ch2" d="M953.0 498h0.0" /><path class="choice ch3" d="M80.0 205a10 10 0 0 0 10 -10v-47a10 10 0 0 1 10 -10" /><g>
<path class="skip" d="M100.0 138h833" /></g><path class="choice ch4" d="M933.0 138a10 10 0 0 1 10 10v340a10 10 0 0 0 10 10" /><path class="choice ch5" d="M80.0 205h20" /><g>
<path class="oneor oom1" d="M100.0 205h0.0" /><path class="oneor oom2" d="M933.0 498h0.0" /><path class="oneor oom3" d="M100.0 205h10" /><g>
<path class="group gr1" d="M110.0 205h0.0" /><path class="group gr2" d="M923.0 498h0.0" /><rect class="group-box" height="396" rx="10" ry="10" width="813" x="110" y="162"></rect><g>
<path class="stack stack1" d="M110.0 205h10.0" /><path class="stack stack2" d="M120.0 205h10" /><g>
<path class="group gr1" d="M130.0 205h238.5" /><path class="group gr2" d="M664.5 205h238.5" /><rect class="group-box" height="38" rx="10" ry="10" width="296" x="368.5" y="186"></rect><g>
<path class="seq seq1" d="M368.5 205h10.0" /><path class="seq seq2" d="M654.5 205h10.0" /><g>
<path class="stack stack1" d="M378.5 205h0.0" /><g class="terminal ">
<path class="terminal term1" d="M378.5 205h10.0" /><path class="terminal term2" d="M480.5 205h10.0" /><rect height="22" rx="10" ry="10" width="92" x="388.5" y="194"></rect><text x="434.5" y="209">School = </text></g><path class="stack stack5" d="M490.5 205h0.0" /></g><path class="seq seq4" d="M490.5 205h10" /><path class="seq seq3" d="M500.5 205h10" /><g class="non-terminal ">
<path class="nonterm nt1" d="M510.5 205h0.0" /><path class="nonterm nt2" d="M578.5 205h0.0" /><rect height="22" width="68" x="510.5" y="194"></rect><text x="544.5" y="209">[^\n]+</text></g><path class="seq seq4" d="M578.5 205h10" /><path class="seq seq3" d="M588.5 205h10" /><g>
<path class="stack stack1" d="M598.5 205h0.0" /><g class="terminal ">
<path class="terminal term1" d="M598.5 205h10.0" /><path class="terminal term2" d="M644.5 205h10.0" /><rect height="22" rx="10" ry="10" width="36" x="608.5" y="194"></rect><text x="626.5" y="209">\n</text></g><path class="stack stack5" d="M654.5 205h0.0" /></g></g><g class="non-terminal ">
<path class="comment com1" d="M368.5 178h0.0" /><path class="comment com2" d="M462.5 178h0.0" /><text class="comment" x="415.5" y="183">field:school</text></g></g><path class="stack stack3" d="M903.0 205a10 10 0 0 1 10 10v7a10 10 0 0 1 -10 10h-773a10 10 0 0 0 -10 10v79a10 10 0 0 0 10 10h10" /><g>
<path class="group gr1" d="M130.0 331h10.0" /><path class="group gr2" d="M893.0 498h10.0" /><rect class="group-box" height="294" rx="10" ry="10" width="753" x="140" y="256"></rect><g>
<path class="choice ch1" d="M140.0 331h0.0" /><path class="choice ch2" d="M893.0 498h0.0" /><path class="choice ch3" d="M140.0 331a10 10 0 0 0 10 -10v-47a10 10 0 0 1 10 -10" /><g>
<path class="skip" d="M160.0 264h713" /></g><path class="choice ch4" d="M873.0 264a10 10 0 0 1 10 10v214a10 10 0 0 0 10 10" /><path class="choice ch5" d="M140.0 331h20" /><g>
<path class="oneor oom1" d="M160.0 331h0.0" /><path class="oneor oom2" d="M873.0 498h0.0" /><path class="oneor oom3" d="M160.0 331h10" /><g>
<path class="seq seq1" d="M170.0 331h0.0" /><path class="seq seq2" d="M863.0 498h0.0" /><g>
<path class="group gr1" d="M170.0 331h0.0" /><path class="group gr2" d="M686.0 498h0.0" /><rect class="group-box" height="246" rx="10" ry="10" width="516" x="170" y="288"></rect><g>
<path class="stack stack1" d="M170.0 331h10.0" /><path class="stack stack2" d="M180.0 331h10" /><g>
<path class="group gr1" d="M190.0 331h63.0" /><path class="group gr2" d="M603.0 331h63.0" /><rect class="group-box" height="38" rx="10" ry="10" width="350" x="253" y="312"></rect><g>
<path class="seq seq1" d="M253.0 331h10.0" /><path class="seq seq2" d="M593.0 331h10.0" /><g>
<path class="stack stack1" d="M263.0 331h0.0" /><g class="terminal ">
<path class="terminal term1" d="M263.0 331h10.0" /><path class="terminal term2" d="M357.0 331h10.0" /><rect height="22" rx="10" ry="10" width="84" x="273" y="320"></rect><text x="315" y="335">Grade = </text></g><path class="stack stack5" d="M367.0 331h0.0" /></g><path class="seq seq4" d="M367.0 331h10" /><path class="seq seq3" d="M377.0 331h10" /><g>
<path class="seq seq1" d="M387.0 331h0.0" /><path class="seq seq2" d="M517.0 331h0.0" /><g class="non-terminal ">
<path class="nonterm nt1" d="M387.0 331h0.0" /><path class="nonterm nt2" d="M431.0 331h0.0" /><rect height="22" width="44" x="387" y="320"></rect><text x="409" y="335">\d+</text></g><path class="seq seq4" d="M431.0 331h10" /><path class="seq seq3" d="M441.0 331h10" /><g class="non-terminal ">
<path class="comment com1" d="M451.0 331h0.0" /><path class="comment com2" d="M517.0 331h0.0" /><text class="comment" x="484" y="336">map: int</text></g></g><path class="seq seq4" d="M517.0 331h10" /><path class="seq seq3" d="M527.0 331h10" /><g>
<path class="stack stack1" d="M537.0 331h0.0" /><g class="terminal ">
<path class="terminal term1" d="M537.0 331h10.0" /><path class="terminal term2" d="M583.0 331h10.0" /><rect height="22" rx="10" ry="10" width="36" x="547" y="320"></rect><text x="565" y="335">\n</text></g><path class="stack stack5" d="M593.0 331h0.0" /></g></g><g class="non-terminal ">
<path class="comment com1" d="M253.0 304h0.0" /><path class="comment com2" d="M340.0 304h0.0" /><text class="comment" x="296.5" y="309">field:grade</text></g></g><path class="stack stack3" d="M666.0 331a10 10 0 0 1 10 10v7a10 10 0 0 1 -10 10h-476a10 10 0 0 0 -10 10v32a10 10 0 0 0 10 10h10" /><g>
<path class="group gr1" d="M190.0 410h10.0" /><path class="group gr2" d="M656.0 410h10.0" /><rect class="group-box" height="56" rx="10" ry="10" width="456" x="200" y="382"></rect><g>
<path class="seq seq1" d="M200.0 410h10.0" /><path class="seq seq2" d="M646.0 410h10.0" /><g>
<path class="stack stack1" d="M210.0 410h0.0" /><g class="terminal ">
<path class="terminal term1" d="M210.0 410h10.0" /><path class="terminal term2" d="M416.0 410h10.0" /><rect height="22" rx="10" ry="10" width="196" x="220" y="399"></rect><text x="318" y="414">Student number, Name\n</text></g><path class="stack stack5" d="M426.0 410h0.0" /></g><path class="seq seq4" d="M426.0 410h10" /><g>
<path class="choice ch1" d="M436.0 410h0.0" /><path class="choice ch2" d="M572.0 410h0.0" /><path class="choice ch3" d="M436.0 410a10 10 0 0 0 10 -10v0a10 10 0 0 1 10 -10" /><g>
<path class="skip" d="M456.0 390h96" /></g><path class="choice ch4" d="M552.0 390a10 10 0 0 1 10 10v0a10 10 0 0 0 10 10" /><path class="choice ch5" d="M436.0 410h20" /><g>
<path class="oneor oom1" d="M456.0 410h0.0" /><path class="oneor oom2" d="M552.0 410h0.0" /><path class="oneor oom3" d="M456.0 410h10" /><g class="non-terminal ">
<path class="nonterm nt1" d="M466.0 410h0.0" /><path class="nonterm nt2" d="M542.0 410h0.0" /><rect height="22" width="76" x="466" y="399"></rect><text x="504" y="414">Student</text></g><path class="oneor oom4" d="M542.0 410h10" /><path class="oneor oom5" d="M466.0 410a10 10 0 0 0 -10 10v0a10 10 0 0 0 10 10" /><g>
<path class="skip" d="M466.0 430h76" /></g><path class="oneor oom6" d="M542.0 430a10 10 0 0 0 10 -10v0a10 10 0 0 0 -10 -10" /></g><path class="choice ch6" d="M552.0 410h20" /></g><path class="seq seq3" d="M572.0 410h10" /><g>
<path class="stack stack1" d="M582.0 410h0.0" /><g class="non-terminal ">
<path class="nonterm nt1" d="M582.0 410h10.0" /><path class="nonterm nt2" d="M636.0 410h10.0" /><rect height="22" width="44" x="592" y="399"></rect><text x="614" y="414">\n*</text></g><path class="stack stack5" d="M646.0 410h0.0" /></g></g><g class="non-terminal ">
<path class="comment com1" d="M200.0 374h0.0" /><path class="comment com2" d="M308.0 374h0.0" /><text class="comment" x="254" y="379">field:students</text></g></g><path class="stack stack3" d="M666.0 410a10 10 0 0 1 10 10v16a10 10 0 0 1 -10 10h-476a10 10 0 0 0 -10 10v32a10 10 0 0 0 10 10h10" /><g>
<path class="group gr1" d="M190.0 498h14.0" /><path class="group gr2" d="M652.0 498h14.0" /><rect class="group-box" height="56" rx="10" ry="10" width="448" x="204" y="470"></rect><g>
<path class="seq seq1" d="M204.0 498h10.0" /><path class="seq seq2" d="M642.0 498h10.0" /><g>
<path class="stack stack1" d="M214.0 498h0.0" /><g class="terminal ">
<path class="terminal term1" d="M214.0 498h10.0" /><path class="terminal term2" d="M428.0 498h10.0" /><rect height="22" rx="10" ry="10" width="204" x="224" y="487"></rect><text x="326" y="502">Student number, Score\n</text></g><path class="stack stack5" d="M438.0 498h0.0" /></g><path class="seq seq4" d="M438.0 498h10" /><g>
<path class="choice ch1" d="M448.0 498h0.0" /><path class="choice ch2" d="M568.0 498h0.0" /><path class="choice ch3" d="M448.0 498a10 10 0 0 0 10 -10v0a10 10 0 0 1 10 -10" /><g>
<path class="skip" d="M468.0 478h80" /></g><path class="choice ch4" d="M548.0 478a10 10 0 0 1 10 10v0a10 10 0 0 0 10 10" /><path class="choice ch5" d="M448.0 498h20" /><g>
<path class="oneor oom1" d="M468.0 498h0.0" /><path class="oneor oom2" d="M548.0 498h0.0" /><path class="oneor oom3" d="M468.0 498h10" /><g class="non-terminal ">
<path class="nonterm nt1" d="M478.0 498h0.0" /><path class="nonterm nt2" d="M538.0 498h0.0" /><rect height="22" width="60" x="478" y="487"></rect><text x="508" y="502">Score</text></g><path class="oneor oom4" d="M538.0 498h10" /><path class="oneor oom5" d="M478.0 498a10 10 0 0 0 -10 10v0a10 10 0 0 0 10 10" /><g>
<path class="skip" d="M478.0 518h60" /></g><path class="oneor oom6" d="M538.0 518a10 10 0 0 0 10 -10v0a10 10 0 0 0 -10 -10" /></g><path class="choice ch6" d="M548.0 498h20" /></g><path class="seq seq3" d="M568.0 498h10" /><g>
<path class="stack stack1" d="M578.0 498h0.0" /><g class="non-terminal ">
<path class="nonterm nt1" d="M578.0 498h10.0" /><path class="nonterm nt2" d="M632.0 498h10.0" /><rect height="22" width="44" x="588" y="487"></rect><text x="610" y="502">\n*</text></g><path class="stack stack5" d="M642.0 498h0.0" /></g></g><g class="non-terminal ">
<path class="comment com1" d="M204.0 462h0.0" /><path class="comment com2" d="M298.0 462h0.0" /><text class="comment" x="251" y="467">field:scores</text></g></g><path class="stack stack4" d="M666.0 498h10" /><path class="stack stack5" d="M676.0 498h10.0" /></g><g class="non-terminal ">
<path class="comment com1" d="M170.0 280h0.0" /><path class="comment com2" d="M250.0 280h0.0" /><text class="comment" x="210" y="285">GradeInput</text></g></g><path class="seq seq4" d="M686.0 498h10" /><path class="seq seq3" d="M696.0 498h10" /><g class="non-terminal ">
<path class="comment com1" d="M706.0 498h0.0" /><path class="comment com2" d="M863.0 498h0.0" /><text class="comment" x="784.5" y="503">map: from_input_grade</text></g></g><path class="oneor oom4" d="M863.0 498h10" /><path class="oneor oom5" d="M170.0 331a10 10 0 0 0 -10 10v191a10 10 0 0 0 10 10" /><g>
<path class="skip" d="M170.0 542h693" /></g><path class="oneor oom6" d="M863.0 542a10 10 0 0 0 10 -10v-24a10 10 0 0 0 -10 -10" /></g><path class="choice ch6" d="M873.0 498h20" /></g><g class="non-terminal ">
<path class="comment com1" d="M140.0 248h0.0" /><path class="comment com2" d="M234.0 248h0.0" /><text class="comment" x="187" y="253">field:grades</text></g></g><path class="stack stack4" d="M903.0 498h10" /><path class="stack stack5" d="M913.0 498h10.0" /></g><g class="non-terminal ">
<path class="comment com1" d="M110.0 154h0.0" /><path class="comment com2" d="M162.0 154h0.0" /><text class="comment" x="136" y="159">School</text></g></g><path class="oneor oom4" d="M923.0 498h10" /><path class="oneor oom5" d="M110.0 205a10 10 0 0 0 -10 10v341a10 10 0 0 0 10 10" /><g>
<path class="skip" d="M110.0 566h813" /></g><path class="oneor oom6" d="M923.0 566a10 10 0 0 0 10 -10v-48a10 10 0 0 0 -10 -10" /></g><path class="choice ch6" d="M933.0 498h20" /></g><g class="non-terminal ">
<path class="comment com1" d="M80.0 122h0.0" /><path class="comment com2" d="M181.0 122h0.0" /><text class="comment" x="130.5" y="127">field:schools</text></g></g><path class="stack stack4" d="M963.0 498h10" /><path class="stack stack5" d="M973.0 498h10.0" /></g><g class="non-terminal ">
<path class="comment com1" d="M50.0 28h0.0" /><path class="comment com2" d="M88.0 28h0.0" /><text class="comment" x="69" y="33">File</text></g></g><path d="M983 498h10" /><path class="end" d="M 993 498 h 20 m -10 -10 v 20 m 10 -20 v 20"></path></g><style>/* <![CDATA[ */
svg.railroad-diagram {
    background-color:hsl(30,20%,95%);
}
svg.railroad-diagram path {
    stroke-width:3;
    stroke:black;
    fill:rgba(0,0,0,0);
}
svg.railroad-diagram path.start {
    stroke-width:3;
    stroke:black;
    fill:rgba(0,0,0,1);
}
svg.railroad-diagram polygon {
    stroke-width:3;
    stroke:black;
    fill:hsl(120,100%,90%);
}
svg.railroad-diagram text {
    font:bold 14px monospace;
    text-anchor:middle;
}
svg.railroad-diagram text.label{
    text-anchor:start;
}
svg.railroad-diagram text.comment{
    font:italic 12px monospace;
}
svg.railroad-diagram rect{
    stroke-width:3;
    stroke:black;
    fill:hsl(120,100%,90%);
}
svg.railroad-diagram rect.group-box {
    stroke: gray;
    stroke-dasharray: 10 5;
    fill: none;
}
/* ]]> */
</style></svg>