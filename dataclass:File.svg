<svg class="railroad-diagram" height="866" viewBox="0 0 1243 866" width="1243" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g transform="translate(.5 .5)">
<g>
<path class="start" d="M20 69v20m10 -20v20m-10 -10h20" /></g><path d="M40 79h10" /><g>
<path class="group gr1" d="M50 79h0.0" /><path class="group gr2" d="M1193.0 747h0.0" /><rect class="group-box" height="810" rx="10" ry="10" width="1143" x="50" y="36"></rect><g>
<path class="stack stack1" d="M50.0 79h10.0" /><path class="stack stack2" d="M60.0 79h10" /><g>
<path class="group gr1" d="M70.0 79h451.5" /><path class="group gr2" d="M721.5 79h451.5" /><rect class="group-box" height="38" rx="10" ry="10" width="200" x="521.5" y="60"></rect><g class="non-terminal ">
<path class="nonterm nt1" d="M521.5 79h10.0" /><path class="nonterm nt2" d="M711.5 79h10.0" /><rect height="22" width="180" x="531.5" y="68"></rect><text x="621.5" y="83">[\s\S]*?(?=School =)</text></g><g class="non-terminal ">
<path class="comment com1" d="M521.5 52h0.0" /><path class="comment com2" d="M615.5 52h0.0" /><text class="comment" x="568.5" y="57">field:header</text></g></g><path class="stack stack3" d="M1173.0 79a10 10 0 0 1 10 10v7a10 10 0 0 1 -10 10h-1103a10 10 0 0 0 -10 10v79a10 10 0 0 0 10 10h10" /><g>
<path class="group gr1" d="M70.0 205h10.0" /><path class="group gr2" d="M1163.0 747h10.0" /><rect class="group-box" height="708" rx="10" ry="10" width="1083" x="80" y="130"></rect><g>
<path class="choice ch1" d="M80.0 205h0.0" /><path class="choice ch2" d="M1163.0 747h0.0" /><path class="choice ch3" d="M80.0 205a10 10 0 0 0 10 -10v-47a10 10 0 0 1 10 -10" /><g>
<path class="skip" d="M100.0 138h1043" /></g><path class="choice ch4" d="M1143.0 138a10 10 0 0 1 10 10v589a10 10 0 0 0 10 10" /><path class="choice ch5" d="M80.0 205h20" /><g>
<path class="oneor oom1" d="M100.0 205h0.0" /><path class="oneor oom2" d="M1143.0 747h0.0" /><path class="oneor oom3" d="M100.0 205h10" /><g>
<path class="group gr1" d="M110.0 205h0.0" /><path class="group gr2" d="M1133.0 747h0.0" /><rect class="group-box" height="660" rx="10" ry="10" width="1023" x="110" y="162"></rect><g>
<path class="stack stack1" d="M110.0 205h10.0" /><path class="stack stack2" d="M120.0 205h10" /><g>
<path class="group gr1" d="M130.0 205h347.5" /><path class="group gr2" d="M765.5 205h347.5" /><rect class="group-box" height="38" rx="10" ry="10" width="288" x="477.5" y="186"></rect><g>
<path class="seq seq1" d="M477.5 205h10.0" /><path class="seq seq2" d="M755.5 205h10.0" /><g>
<path class="stack stack1" d="M487.5 205h0.0" /><g class="terminal ">
<path class="terminal term1" d="M487.5 205h10.0" /><path class="terminal term2" d="M589.5 205h10.0" /><rect height="22" rx="10" ry="10" width="92" x="497.5" y="194"></rect><text x="543.5" y="209">School = </text></g><path class="stack stack5" d="M599.5 205h0.0" /></g><path class="seq seq4" d="M599.5 205h10" /><path class="seq seq3" d="M609.5 205h10" /><g class="non-terminal ">
<path class="nonterm nt1" d="M619.5 205h0.0" /><path class="nonterm nt2" d="M687.5 205h0.0" /><rect height="22" width="68" x="619.5" y="194"></rect><text x="653.5" y="209">[^\n]+</text></g><path class="seq seq4" d="M687.5 205h10" /><path class="seq seq3" d="M697.5 205h10" /><g>
<path class="stack stack1" d="M707.5 205h0.0" /><g class="terminal ">
<path class="terminal term1" d="M707.5 205h10.0" /><path class="terminal term2" d="M745.5 205h10.0" /><rect height="22" rx="10" ry="10" width="28" x="717.5" y="194"></rect><text x="731.5" y="209">
</text></g><path class="stack stack5" d="M755.5 205h0.0" /></g></g><g class="non-terminal ">
<path class="comment com1" d="M477.5 178h0.0" /><path class="comment com2" d="M571.5 178h0.0" /><text class="comment" x="524.5" y="183">field:school</text></g></g><path class="stack stack3" d="M1113.0 205a10 10 0 0 1 10 10v7a10 10 0 0 1 -10 10h-983a10 10 0 0 0 -10 10v79a10 10 0 0 0 10 10h10" /><g>
<path class="group gr1" d="M130.0 331h10.0" /><path class="group gr2" d="M1103.0 747h10.0" /><rect class="group-box" height="558" rx="10" ry="10" width="963" x="140" y="256"></rect><g>
<path class="choice ch1" d="M140.0 331h0.0" /><path class="choice ch2" d="M1103.0 747h0.0" /><path class="choice ch3" d="M140.0 331a10 10 0 0 0 10 -10v-47a10 10 0 0 1 10 -10" /><g>
<path class="skip" d="M160.0 264h923" /></g><path class="choice ch4" d="M1083.0 264a10 10 0 0 1 10 10v463a10 10 0 0 0 10 10" /><path class="choice ch5" d="M140.0 331h20" /><g>
<path class="oneor oom1" d="M160.0 331h0.0" /><path class="oneor oom2" d="M1083.0 747h0.0" /><path class="oneor oom3" d="M160.0 331h10" /><g>
<path class="seq seq1" d="M170.0 331h0.0" /><path class="seq seq2" d="M1073.0 747h0.0" /><g>
<path class="group gr1" d="M170.0 331h0.0" /><path class="group gr2" d="M896.0 747h0.0" /><rect class="group-box" height="510" rx="10" ry="10" width="726" x="170" y="288"></rect><g>
<path class="stack stack1" d="M170.0 331h10.0" /><path class="stack stack2" d="M180.0 331h10" /><g>
<path class="group gr1" d="M190.0 331h172.0" /><path class="group gr2" d="M704.0 331h172.0" /><rect class="group-box" height="38" rx="10" ry="10" width="342" x="362" y="312"></rect><g>
<path class="seq seq1" d="M362.0 331h10.0" /><path class="seq seq2" d="M694.0 331h10.0" /><g>
<path class="stack stack1" d="M372.0 331h0.0" /><g class="terminal ">
<path class="terminal term1" d="M372.0 331h10.0" /><path class="terminal term2" d="M466.0 331h10.0" /><rect height="22" rx="10" ry="10" width="84" x="382" y="320"></rect><text x="424" y="335">Grade = </text></g><path class="stack stack5" d="M476.0 331h0.0" /></g><path class="seq seq4" d="M476.0 331h10" /><path class="seq seq3" d="M486.0 331h10" /><g>
<path class="seq seq1" d="M496.0 331h0.0" /><path class="seq seq2" d="M626.0 331h0.0" /><g class="non-terminal ">
<path class="nonterm nt1" d="M496.0 331h0.0" /><path class="nonterm nt2" d="M540.0 331h0.0" /><rect height="22" width="44" x="496" y="320"></rect><text x="518" y="335">\d+</text></g><path class="seq seq4" d="M540.0 331h10" /><path class="seq seq3" d="M550.0 331h10" /><g class="non-terminal ">
<path class="comment com1" d="M560.0 331h0.0" /><path class="comment com2" d="M626.0 331h0.0" /><text class="comment" x="593" y="336">map: int</text></g></g><path class="seq seq4" d="M626.0 331h10" /><path class="seq seq3" d="M636.0 331h10" /><g>
<path class="stack stack1" d="M646.0 331h0.0" /><g class="terminal ">
<path class="terminal term1" d="M646.0 331h10.0" /><path class="terminal term2" d="M684.0 331h10.0" /><rect height="22" rx="10" ry="10" width="28" x="656" y="320"></rect><text x="670" y="335">
</text></g><path class="stack stack5" d="M694.0 331h0.0" /></g></g><g class="non-terminal ">
<path class="comment com1" d="M362.0 304h0.0" /><path class="comment com2" d="M449.0 304h0.0" /><text class="comment" x="405.5" y="309">field:grade</text></g></g><path class="stack stack3" d="M876.0 331a10 10 0 0 1 10 10v7a10 10 0 0 1 -10 10h-686a10 10 0 0 0 -10 10v79a10 10 0 0 0 10 10h10" /><g>
<path class="group gr1" d="M190.0 457h14.0" /><path class="group gr2" d="M862.0 527h14.0" /><rect class="group-box" height="188" rx="10" ry="10" width="658" x="204" y="382"></rect><g>
<path class="seq seq1" d="M204.0 457h10.0" /><path class="seq seq2" d="M852.0 527h10.0" /><g>
<path class="stack stack1" d="M214.0 457h0.0" /><g class="terminal ">
<path class="terminal term1" d="M214.0 457h10.0" /><path class="terminal term2" d="M412.0 457h10.0" /><rect height="22" rx="10" ry="10" width="188" x="224" y="446"></rect><text x="318" y="461">Student number, Name
</text></g><path class="stack stack5" d="M422.0 457h0.0" /></g><path class="seq seq4" d="M422.0 457h10" /><g>
<path class="choice ch1" d="M432.0 457h0.0" /><path class="choice ch2" d="M778.0 527h0.0" /><path class="choice ch3" d="M432.0 457a10 10 0 0 0 10 -10v-47a10 10 0 0 1 10 -10" /><g>
<path class="skip" d="M452.0 390h306" /></g><path class="choice ch4" d="M758.0 390a10 10 0 0 1 10 10v117a10 10 0 0 0 10 10" /><path class="choice ch5" d="M432.0 457h20" /><g>
<path class="oneor oom1" d="M452.0 457h0.0" /><path class="oneor oom2" d="M758.0 527h0.0" /><path class="oneor oom3" d="M452.0 457h10" /><g>
<path class="group gr1" d="M462.0 457h0.0" /><path class="group gr2" d="M748.0 527h0.0" /><rect class="group-box" height="140" rx="10" ry="10" width="286" x="462" y="414"></rect><g>
<path class="stack stack1" d="M462.0 457h10.0" /><path class="stack stack2" d="M472.0 457h10" /><g>
<path class="group gr1" d="M482.0 457h10.0" /><path class="group gr2" d="M718.0 457h10.0" /><rect class="group-box" height="38" rx="10" ry="10" width="226" x="492" y="438"></rect><g>
<path class="seq seq1" d="M492.0 457h10.0" /><path class="seq seq2" d="M708.0 457h10.0" /><g>
<path class="seq seq1" d="M502.0 457h0.0" /><path class="seq seq2" d="M632.0 457h0.0" /><g class="non-terminal ">
<path class="nonterm nt1" d="M502.0 457h0.0" /><path class="nonterm nt2" d="M546.0 457h0.0" /><rect height="22" width="44" x="502" y="446"></rect><text x="524" y="461">\d+</text></g><path class="seq seq4" d="M546.0 457h10" /><path class="seq seq3" d="M556.0 457h10" /><g class="non-terminal ">
<path class="comment com1" d="M566.0 457h0.0" /><path class="comment com2" d="M632.0 457h0.0" /><text class="comment" x="599" y="462">map: int</text></g></g><path class="seq seq4" d="M632.0 457h10" /><path class="seq seq3" d="M642.0 457h10" /><g>
<path class="stack stack1" d="M652.0 457h0.0" /><g class="terminal ">
<path class="terminal term1" d="M652.0 457h10.0" /><path class="terminal term2" d="M698.0 457h10.0" /><rect height="22" rx="10" ry="10" width="36" x="662" y="446"></rect><text x="680" y="461">, </text></g><path class="stack stack5" d="M708.0 457h0.0" /></g></g><g class="non-terminal ">
<path class="comment com1" d="M492.0 430h0.0" /><path class="comment com2" d="M586.0 430h0.0" /><text class="comment" x="539" y="435">field:number</text></g></g><path class="stack stack3" d="M728.0 457a10 10 0 0 1 10 10v7a10 10 0 0 1 -10 10h-246a10 10 0 0 0 -10 10v23a10 10 0 0 0 10 10h10" /><g>
<path class="group gr1" d="M482.0 527h45.0" /><path class="group gr2" d="M683.0 527h45.0" /><rect class="group-box" height="38" rx="10" ry="10" width="156" x="527" y="508"></rect><g>
<path class="seq seq1" d="M527.0 527h10.0" /><path class="seq seq2" d="M673.0 527h10.0" /><g class="non-terminal ">
<path class="nonterm nt1" d="M537.0 527h0.0" /><path class="nonterm nt2" d="M605.0 527h0.0" /><rect height="22" width="68" x="537" y="516"></rect><text x="571" y="531">[^\n]+</text></g><path class="seq seq4" d="M605.0 527h10" /><path class="seq seq3" d="M615.0 527h10" /><g>
<path class="stack stack1" d="M625.0 527h0.0" /><g class="terminal ">
<path class="terminal term1" d="M625.0 527h10.0" /><path class="terminal term2" d="M663.0 527h10.0" /><rect height="22" rx="10" ry="10" width="28" x="635" y="516"></rect><text x="649" y="531">
</text></g><path class="stack stack5" d="M673.0 527h0.0" /></g></g><g class="non-terminal ">
<path class="comment com1" d="M527.0 500h0.0" /><path class="comment com2" d="M607.0 500h0.0" /><text class="comment" x="567" y="505">field:name</text></g></g><path class="stack stack4" d="M728.0 527h10" /><path class="stack stack5" d="M738.0 527h10.0" /></g><g class="non-terminal ">
<path class="comment com1" d="M462.0 406h0.0" /><path class="comment com2" d="M521.0 406h0.0" /><text class="comment" x="491.5" y="411">Student</text></g></g><path class="oneor oom4" d="M748.0 527h10" /><path class="oneor oom5" d="M462.0 457a10 10 0 0 0 -10 10v85a10 10 0 0 0 10 10" /><g>
<path class="skip" d="M462.0 562h286" /></g><path class="oneor oom6" d="M748.0 562a10 10 0 0 0 10 -10v-15a10 10 0 0 0 -10 -10" /></g><path class="choice ch6" d="M758.0 527h20" /></g><path class="seq seq3" d="M778.0 527h10" /><g>
<path class="stack stack1" d="M788.0 527h0.0" /><g class="non-terminal ">
<path class="nonterm nt1" d="M788.0 527h10.0" /><path class="nonterm nt2" d="M842.0 527h10.0" /><rect height="22" width="44" x="798" y="516"></rect><text x="820" y="531">\n*</text></g><path class="stack stack5" d="M852.0 527h0.0" /></g></g><g class="non-terminal ">
<path class="comment com1" d="M204.0 374h0.0" /><path class="comment com2" d="M312.0 374h0.0" /><text class="comment" x="258" y="379">field:students</text></g></g><path class="stack stack3" d="M876.0 527a10 10 0 0 1 10 10v31a10 10 0 0 1 -10 10h-686a10 10 0 0 0 -10 10v79a10 10 0 0 0 10 10h10" /><g>
<path class="group gr1" d="M190.0 677h10.0" /><path class="group gr2" d="M866.0 747h10.0" /><rect class="group-box" height="188" rx="10" ry="10" width="666" x="200" y="602"></rect><g>
<path class="seq seq1" d="M200.0 677h10.0" /><path class="seq seq2" d="M856.0 747h10.0" /><g>
<path class="stack stack1" d="M210.0 677h0.0" /><g class="terminal ">
<path class="terminal term1" d="M210.0 677h10.0" /><path class="terminal term2" d="M416.0 677h10.0" /><rect height="22" rx="10" ry="10" width="196" x="220" y="666"></rect><text x="318" y="681">Student number, Score
</text></g><path class="stack stack5" d="M426.0 677h0.0" /></g><path class="seq seq4" d="M426.0 677h10" /><g>
<path class="choice ch1" d="M436.0 677h0.0" /><path class="choice ch2" d="M782.0 747h0.0" /><path class="choice ch3" d="M436.0 677a10 10 0 0 0 10 -10v-47a10 10 0 0 1 10 -10" /><g>
<path class="skip" d="M456.0 610h306" /></g><path class="choice ch4" d="M762.0 610a10 10 0 0 1 10 10v117a10 10 0 0 0 10 10" /><path class="choice ch5" d="M436.0 677h20" /><g>
<path class="oneor oom1" d="M456.0 677h0.0" /><path class="oneor oom2" d="M762.0 747h0.0" /><path class="oneor oom3" d="M456.0 677h10" /><g>
<path class="group gr1" d="M466.0 677h0.0" /><path class="group gr2" d="M752.0 747h0.0" /><rect class="group-box" height="140" rx="10" ry="10" width="286" x="466" y="634"></rect><g>
<path class="stack stack1" d="M466.0 677h10.0" /><path class="stack stack2" d="M476.0 677h10" /><g>
<path class="group gr1" d="M486.0 677h10.0" /><path class="group gr2" d="M722.0 677h10.0" /><rect class="group-box" height="38" rx="10" ry="10" width="226" x="496" y="658"></rect><g>
<path class="seq seq1" d="M496.0 677h10.0" /><path class="seq seq2" d="M712.0 677h10.0" /><g>
<path class="seq seq1" d="M506.0 677h0.0" /><path class="seq seq2" d="M636.0 677h0.0" /><g class="non-terminal ">
<path class="nonterm nt1" d="M506.0 677h0.0" /><path class="nonterm nt2" d="M550.0 677h0.0" /><rect height="22" width="44" x="506" y="666"></rect><text x="528" y="681">\d+</text></g><path class="seq seq4" d="M550.0 677h10" /><path class="seq seq3" d="M560.0 677h10" /><g class="non-terminal ">
<path class="comment com1" d="M570.0 677h0.0" /><path class="comment com2" d="M636.0 677h0.0" /><text class="comment" x="603" y="682">map: int</text></g></g><path class="seq seq4" d="M636.0 677h10" /><path class="seq seq3" d="M646.0 677h10" /><g>
<path class="stack stack1" d="M656.0 677h0.0" /><g class="terminal ">
<path class="terminal term1" d="M656.0 677h10.0" /><path class="terminal term2" d="M702.0 677h10.0" /><rect height="22" rx="10" ry="10" width="36" x="666" y="666"></rect><text x="684" y="681">, </text></g><path class="stack stack5" d="M712.0 677h0.0" /></g></g><g class="non-terminal ">
<path class="comment com1" d="M496.0 650h0.0" /><path class="comment com2" d="M590.0 650h0.0" /><text class="comment" x="543" y="655">field:number</text></g></g><path class="stack stack3" d="M732.0 677a10 10 0 0 1 10 10v7a10 10 0 0 1 -10 10h-246a10 10 0 0 0 -10 10v23a10 10 0 0 0 10 10h10" /><g>
<path class="group gr1" d="M486.0 747h14.0" /><path class="group gr2" d="M718.0 747h14.0" /><rect class="group-box" height="38" rx="10" ry="10" width="218" x="500" y="728"></rect><g>
<path class="seq seq1" d="M500.0 747h10.0" /><path class="seq seq2" d="M708.0 747h10.0" /><g>
<path class="seq seq1" d="M510.0 747h0.0" /><path class="seq seq2" d="M640.0 747h0.0" /><g class="non-terminal ">
<path class="nonterm nt1" d="M510.0 747h0.0" /><path class="nonterm nt2" d="M554.0 747h0.0" /><rect height="22" width="44" x="510" y="736"></rect><text x="532" y="751">\d+</text></g><path class="seq seq4" d="M554.0 747h10" /><path class="seq seq3" d="M564.0 747h10" /><g class="non-terminal ">
<path class="comment com1" d="M574.0 747h0.0" /><path class="comment com2" d="M640.0 747h0.0" /><text class="comment" x="607" y="752">map: int</text></g></g><path class="seq seq4" d="M640.0 747h10" /><path class="seq seq3" d="M650.0 747h10" /><g>
<path class="stack stack1" d="M660.0 747h0.0" /><g class="terminal ">
<path class="terminal term1" d="M660.0 747h10.0" /><path class="terminal term2" d="M698.0 747h10.0" /><rect height="22" rx="10" ry="10" width="28" x="670" y="736"></rect><text x="684" y="751">
</text></g><path class="stack stack5" d="M708.0 747h0.0" /></g></g><g class="non-terminal ">
<path class="comment com1" d="M500.0 720h0.0" /><path class="comment com2" d="M587.0 720h0.0" /><text class="comment" x="543.5" y="725">field:score</text></g></g><path class="stack stack4" d="M732.0 747h10" /><path class="stack stack5" d="M742.0 747h10.0" /></g><g class="non-terminal ">
<path class="comment com1" d="M466.0 626h0.0" /><path class="comment com2" d="M511.0 626h0.0" /><text class="comment" x="488.5" y="631">Score</text></g></g><path class="oneor oom4" d="M752.0 747h10" /><path class="oneor oom5" d="M466.0 677a10 10 0 0 0 -10 10v85a10 10 0 0 0 10 10" /><g>
<path class="skip" d="M466.0 782h286" /></g><path class="oneor oom6" d="M752.0 782a10 10 0 0 0 10 -10v-15a10 10 0 0 0 -10 -10" /></g><path class="choice ch6" d="M762.0 747h20" /></g><path class="seq seq3" d="M782.0 747h10" /><g>
<path class="stack stack1" d="M792.0 747h0.0" /><g class="non-terminal ">
<path class="nonterm nt1" d="M792.0 747h10.0" /><path class="nonterm nt2" d="M846.0 747h10.0" /><rect height="22" width="44" x="802" y="736"></rect><text x="824" y="751">\n*</text></g><path class="stack stack5" d="M856.0 747h0.0" /></g></g><g class="non-terminal ">
<path class="comment com1" d="M200.0 594h0.0" /><path class="comment com2" d="M294.0 594h0.0" /><text class="comment" x="247" y="599">field:scores</text></g></g><path class="stack stack4" d="M876.0 747h10" /><path class="stack stack5" d="M886.0 747h10.0" /></g><g class="non-terminal ">
<path class="comment com1" d="M170.0 280h0.0" /><path class="comment com2" d="M250.0 280h0.0" /><text class="comment" x="210" y="285">GradeInput</text></g></g><path class="seq seq4" d="M896.0 747h10" /><path class="seq seq3" d="M906.0 747h10" /><g class="non-terminal ">
<path class="comment com1" d="M916.0 747h0.0" /><path class="comment com2" d="M1073.0 747h0.0" /><text class="comment" x="994.5" y="752">map: from_input_grade</text></g></g><path class="oneor oom4" d="M1073.0 747h10" /><path class="oneor oom5" d="M170.0 331a10 10 0 0 0 -10 10v455a10 10 0 0 0 10 10" /><g>
<path class="skip" d="M170.0 806h903" /></g><path class="oneor oom6" d="M1073.0 806a10 10 0 0 0 10 -10v-39a10 10 0 0 0 -10 -10" /></g><path class="choice ch6" d="M1083.0 747h20" /></g><g class="non-terminal ">
<path class="comment com1" d="M140.0 248h0.0" /><path class="comment com2" d="M234.0 248h0.0" /><text class="comment" x="187" y="253">field:grades</text></g></g><path class="stack stack4" d="M1113.0 747h10" /><path class="stack stack5" d="M1123.0 747h10.0" /></g><g class="non-terminal ">
<path class="comment com1" d="M110.0 154h0.0" /><path class="comment com2" d="M162.0 154h0.0" /><text class="comment" x="136" y="159">School</text></g></g><path class="oneor oom4" d="M1133.0 747h10" /><path class="oneor oom5" d="M110.0 205a10 10 0 0 0 -10 10v605a10 10 0 0 0 10 10" /><g>
<path class="skip" d="M110.0 830h1023" /></g><path class="oneor oom6" d="M1133.0 830a10 10 0 0 0 10 -10v-63a10 10 0 0 0 -10 -10" /></g><path class="choice ch6" d="M1143.0 747h20" /></g><g class="non-terminal ">
<path class="comment com1" d="M80.0 122h0.0" /><path class="comment com2" d="M181.0 122h0.0" /><text class="comment" x="130.5" y="127">field:schools</text></g></g><path class="stack stack4" d="M1173.0 747h10" /><path class="stack stack5" d="M1183.0 747h10.0" /></g><g class="non-terminal ">
<path class="comment com1" d="M50.0 28h0.0" /><path class="comment com2" d="M88.0 28h0.0" /><text class="comment" x="69" y="33">File</text></g></g><path d="M1193 747h10" /><path class="end" d="M 1203 747 h 20 m -10 -10 v 20 m 10 -20 v 20"></path></g><style>/* <![CDATA[ */
svg.railroad-diagram {
    background-color:hsl(30,20%,95%);
}
svg.railroad-diagram path {
    stroke-width:3;
    stroke:black;
    fill:rgba(0,0,0,0);
}
svg.railroad-diagram path.start {
    stroke-width:3;
    stroke:black;
    fill:rgba(0,0,0,1);
}
svg.railroad-diagram polygon {
    stroke-width:3;
    stroke:black;
    fill:hsl(120,100%,90%);
}
svg.railroad-diagram text {
    font:bold 14px monospace;
    text-anchor:middle;
}
svg.railroad-diagram text.label{
    text-anchor:start;
}
svg.railroad-diagram text.comment{
    font:italic 12px monospace;
}
svg.railroad-diagram rect{
    stroke-width:3;
    stroke:black;
    fill:hsl(120,100%,90%);
}
svg.railroad-diagram rect.group-box {
    stroke: gray;
    stroke-dasharray: 10 5;
    fill: none;
}
/* ]]> */
</style></svg>