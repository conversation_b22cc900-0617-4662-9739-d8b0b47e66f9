<svg class="railroad-diagram" height="866" viewBox="0 0 1251 866" width="1251" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g transform="translate(.5 .5)">
<g>
<path class="start" d="M20 69v20m10 -20v20m-10 -10h20" /></g><path d="M40 79h10" /><g>
<path class="group gr1" d="M50 79h0.0" /><path class="group gr2" d="M1201.0 747h0.0" /><rect class="group-box" height="810" rx="10" ry="10" width="1151" x="50" y="36"></rect><g>
<path class="stack stack1" d="M50.0 79h10.0" /><path class="stack stack2" d="M60.0 79h10" /><g>
<path class="group gr1" d="M70.0 79h455.5" /><path class="group gr2" d="M725.5 79h455.5" /><rect class="group-box" height="38" rx="10" ry="10" width="200" x="525.5" y="60"></rect><g class="non-terminal ">
<path class="nonterm nt1" d="M525.5 79h10.0" /><path class="nonterm nt2" d="M715.5 79h10.0" /><rect height="22" width="180" x="535.5" y="68"></rect><text x="625.5" y="83">[\s\S]*?(?=School =)</text></g><g class="non-terminal ">
<path class="comment com1" d="M525.5 52h0.0" /><path class="comment com2" d="M619.5 52h0.0" /><text class="comment" x="572.5" y="57">field:header</text></g></g><path class="stack stack3" d="M1181.0 79a10 10 0 0 1 10 10v7a10 10 0 0 1 -10 10h-1111a10 10 0 0 0 -10 10v79a10 10 0 0 0 10 10h10" /><g>
<path class="group gr1" d="M70.0 205h10.0" /><path class="group gr2" d="M1171.0 747h10.0" /><rect class="group-box" height="708" rx="10" ry="10" width="1091" x="80" y="130"></rect><g>
<path class="choice ch1" d="M80.0 205h0.0" /><path class="choice ch2" d="M1171.0 747h0.0" /><path class="choice ch3" d="M80.0 205a10 10 0 0 0 10 -10v-47a10 10 0 0 1 10 -10" /><g>
<path class="skip" d="M100.0 138h1051" /></g><path class="choice ch4" d="M1151.0 138a10 10 0 0 1 10 10v589a10 10 0 0 0 10 10" /><path class="choice ch5" d="M80.0 205h20" /><g>
<path class="oneor oom1" d="M100.0 205h0.0" /><path class="oneor oom2" d="M1151.0 747h0.0" /><path class="oneor oom3" d="M100.0 205h10" /><g>
<path class="group gr1" d="M110.0 205h0.0" /><path class="group gr2" d="M1141.0 747h0.0" /><rect class="group-box" height="660" rx="10" ry="10" width="1031" x="110" y="162"></rect><g>
<path class="stack stack1" d="M110.0 205h10.0" /><path class="stack stack2" d="M120.0 205h10" /><g>
<path class="group gr1" d="M130.0 205h347.5" /><path class="group gr2" d="M773.5 205h347.5" /><rect class="group-box" height="38" rx="10" ry="10" width="296" x="477.5" y="186"></rect><g>
<path class="seq seq1" d="M477.5 205h10.0" /><path class="seq seq2" d="M763.5 205h10.0" /><g>
<path class="stack stack1" d="M487.5 205h0.0" /><g class="terminal ">
<path class="terminal term1" d="M487.5 205h10.0" /><path class="terminal term2" d="M589.5 205h10.0" /><rect height="22" rx="10" ry="10" width="92" x="497.5" y="194"></rect><text x="543.5" y="209">School = </text></g><path class="stack stack5" d="M599.5 205h0.0" /></g><path class="seq seq4" d="M599.5 205h10" /><path class="seq seq3" d="M609.5 205h10" /><g class="non-terminal ">
<path class="nonterm nt1" d="M619.5 205h0.0" /><path class="nonterm nt2" d="M687.5 205h0.0" /><rect height="22" width="68" x="619.5" y="194"></rect><text x="653.5" y="209">[^\n]+</text></g><path class="seq seq4" d="M687.5 205h10" /><path class="seq seq3" d="M697.5 205h10" /><g>
<path class="stack stack1" d="M707.5 205h0.0" /><g class="terminal ">
<path class="terminal term1" d="M707.5 205h10.0" /><path class="terminal term2" d="M753.5 205h10.0" /><rect height="22" rx="10" ry="10" width="36" x="717.5" y="194"></rect><text x="735.5" y="209">\n</text></g><path class="stack stack5" d="M763.5 205h0.0" /></g></g><g class="non-terminal ">
<path class="comment com1" d="M477.5 178h0.0" /><path class="comment com2" d="M571.5 178h0.0" /><text class="comment" x="524.5" y="183">field:school</text></g></g><path class="stack stack3" d="M1121.0 205a10 10 0 0 1 10 10v7a10 10 0 0 1 -10 10h-991a10 10 0 0 0 -10 10v79a10 10 0 0 0 10 10h10" /><g>
<path class="group gr1" d="M130.0 331h10.0" /><path class="group gr2" d="M1111.0 747h10.0" /><rect class="group-box" height="558" rx="10" ry="10" width="971" x="140" y="256"></rect><g>
<path class="choice ch1" d="M140.0 331h0.0" /><path class="choice ch2" d="M1111.0 747h0.0" /><path class="choice ch3" d="M140.0 331a10 10 0 0 0 10 -10v-47a10 10 0 0 1 10 -10" /><g>
<path class="skip" d="M160.0 264h931" /></g><path class="choice ch4" d="M1091.0 264a10 10 0 0 1 10 10v463a10 10 0 0 0 10 10" /><path class="choice ch5" d="M140.0 331h20" /><g>
<path class="oneor oom1" d="M160.0 331h0.0" /><path class="oneor oom2" d="M1091.0 747h0.0" /><path class="oneor oom3" d="M160.0 331h10" /><g>
<path class="seq seq1" d="M170.0 331h0.0" /><path class="seq seq2" d="M1081.0 747h0.0" /><g>
<path class="group gr1" d="M170.0 331h0.0" /><path class="group gr2" d="M904.0 747h0.0" /><rect class="group-box" height="510" rx="10" ry="10" width="734" x="170" y="288"></rect><g>
<path class="stack stack1" d="M170.0 331h10.0" /><path class="stack stack2" d="M180.0 331h10" /><g>
<path class="group gr1" d="M190.0 331h172.0" /><path class="group gr2" d="M712.0 331h172.0" /><rect class="group-box" height="38" rx="10" ry="10" width="350" x="362" y="312"></rect><g>
<path class="seq seq1" d="M362.0 331h10.0" /><path class="seq seq2" d="M702.0 331h10.0" /><g>
<path class="stack stack1" d="M372.0 331h0.0" /><g class="terminal ">
<path class="terminal term1" d="M372.0 331h10.0" /><path class="terminal term2" d="M466.0 331h10.0" /><rect height="22" rx="10" ry="10" width="84" x="382" y="320"></rect><text x="424" y="335">Grade = </text></g><path class="stack stack5" d="M476.0 331h0.0" /></g><path class="seq seq4" d="M476.0 331h10" /><path class="seq seq3" d="M486.0 331h10" /><g>
<path class="seq seq1" d="M496.0 331h0.0" /><path class="seq seq2" d="M626.0 331h0.0" /><g class="non-terminal ">
<path class="nonterm nt1" d="M496.0 331h0.0" /><path class="nonterm nt2" d="M540.0 331h0.0" /><rect height="22" width="44" x="496" y="320"></rect><text x="518" y="335">\d+</text></g><path class="seq seq4" d="M540.0 331h10" /><path class="seq seq3" d="M550.0 331h10" /><g class="non-terminal ">
<path class="comment com1" d="M560.0 331h0.0" /><path class="comment com2" d="M626.0 331h0.0" /><text class="comment" x="593" y="336">map: int</text></g></g><path class="seq seq4" d="M626.0 331h10" /><path class="seq seq3" d="M636.0 331h10" /><g>
<path class="stack stack1" d="M646.0 331h0.0" /><g class="terminal ">
<path class="terminal term1" d="M646.0 331h10.0" /><path class="terminal term2" d="M692.0 331h10.0" /><rect height="22" rx="10" ry="10" width="36" x="656" y="320"></rect><text x="674" y="335">\n</text></g><path class="stack stack5" d="M702.0 331h0.0" /></g></g><g class="non-terminal ">
<path class="comment com1" d="M362.0 304h0.0" /><path class="comment com2" d="M449.0 304h0.0" /><text class="comment" x="405.5" y="309">field:grade</text></g></g><path class="stack stack3" d="M884.0 331a10 10 0 0 1 10 10v7a10 10 0 0 1 -10 10h-694a10 10 0 0 0 -10 10v79a10 10 0 0 0 10 10h10" /><g>
<path class="group gr1" d="M190.0 457h14.0" /><path class="group gr2" d="M870.0 527h14.0" /><rect class="group-box" height="188" rx="10" ry="10" width="666" x="204" y="382"></rect><g>
<path class="seq seq1" d="M204.0 457h10.0" /><path class="seq seq2" d="M860.0 527h10.0" /><g>
<path class="stack stack1" d="M214.0 457h0.0" /><g class="terminal ">
<path class="terminal term1" d="M214.0 457h10.0" /><path class="terminal term2" d="M420.0 457h10.0" /><rect height="22" rx="10" ry="10" width="196" x="224" y="446"></rect><text x="322" y="461">Student number, Name\n</text></g><path class="stack stack5" d="M430.0 457h0.0" /></g><path class="seq seq4" d="M430.0 457h10" /><g>
<path class="choice ch1" d="M440.0 457h0.0" /><path class="choice ch2" d="M786.0 527h0.0" /><path class="choice ch3" d="M440.0 457a10 10 0 0 0 10 -10v-47a10 10 0 0 1 10 -10" /><g>
<path class="skip" d="M460.0 390h306" /></g><path class="choice ch4" d="M766.0 390a10 10 0 0 1 10 10v117a10 10 0 0 0 10 10" /><path class="choice ch5" d="M440.0 457h20" /><g>
<path class="oneor oom1" d="M460.0 457h0.0" /><path class="oneor oom2" d="M766.0 527h0.0" /><path class="oneor oom3" d="M460.0 457h10" /><g>
<path class="group gr1" d="M470.0 457h0.0" /><path class="group gr2" d="M756.0 527h0.0" /><rect class="group-box" height="140" rx="10" ry="10" width="286" x="470" y="414"></rect><g>
<path class="stack stack1" d="M470.0 457h10.0" /><path class="stack stack2" d="M480.0 457h10" /><g>
<path class="group gr1" d="M490.0 457h10.0" /><path class="group gr2" d="M726.0 457h10.0" /><rect class="group-box" height="38" rx="10" ry="10" width="226" x="500" y="438"></rect><g>
<path class="seq seq1" d="M500.0 457h10.0" /><path class="seq seq2" d="M716.0 457h10.0" /><g>
<path class="seq seq1" d="M510.0 457h0.0" /><path class="seq seq2" d="M640.0 457h0.0" /><g class="non-terminal ">
<path class="nonterm nt1" d="M510.0 457h0.0" /><path class="nonterm nt2" d="M554.0 457h0.0" /><rect height="22" width="44" x="510" y="446"></rect><text x="532" y="461">\d+</text></g><path class="seq seq4" d="M554.0 457h10" /><path class="seq seq3" d="M564.0 457h10" /><g class="non-terminal ">
<path class="comment com1" d="M574.0 457h0.0" /><path class="comment com2" d="M640.0 457h0.0" /><text class="comment" x="607" y="462">map: int</text></g></g><path class="seq seq4" d="M640.0 457h10" /><path class="seq seq3" d="M650.0 457h10" /><g>
<path class="stack stack1" d="M660.0 457h0.0" /><g class="terminal ">
<path class="terminal term1" d="M660.0 457h10.0" /><path class="terminal term2" d="M706.0 457h10.0" /><rect height="22" rx="10" ry="10" width="36" x="670" y="446"></rect><text x="688" y="461">, </text></g><path class="stack stack5" d="M716.0 457h0.0" /></g></g><g class="non-terminal ">
<path class="comment com1" d="M500.0 430h0.0" /><path class="comment com2" d="M594.0 430h0.0" /><text class="comment" x="547" y="435">field:number</text></g></g><path class="stack stack3" d="M736.0 457a10 10 0 0 1 10 10v7a10 10 0 0 1 -10 10h-246a10 10 0 0 0 -10 10v23a10 10 0 0 0 10 10h10" /><g>
<path class="group gr1" d="M490.0 527h41.0" /><path class="group gr2" d="M695.0 527h41.0" /><rect class="group-box" height="38" rx="10" ry="10" width="164" x="531" y="508"></rect><g>
<path class="seq seq1" d="M531.0 527h10.0" /><path class="seq seq2" d="M685.0 527h10.0" /><g class="non-terminal ">
<path class="nonterm nt1" d="M541.0 527h0.0" /><path class="nonterm nt2" d="M609.0 527h0.0" /><rect height="22" width="68" x="541" y="516"></rect><text x="575" y="531">[^\n]+</text></g><path class="seq seq4" d="M609.0 527h10" /><path class="seq seq3" d="M619.0 527h10" /><g>
<path class="stack stack1" d="M629.0 527h0.0" /><g class="terminal ">
<path class="terminal term1" d="M629.0 527h10.0" /><path class="terminal term2" d="M675.0 527h10.0" /><rect height="22" rx="10" ry="10" width="36" x="639" y="516"></rect><text x="657" y="531">\n</text></g><path class="stack stack5" d="M685.0 527h0.0" /></g></g><g class="non-terminal ">
<path class="comment com1" d="M531.0 500h0.0" /><path class="comment com2" d="M611.0 500h0.0" /><text class="comment" x="571" y="505">field:name</text></g></g><path class="stack stack4" d="M736.0 527h10" /><path class="stack stack5" d="M746.0 527h10.0" /></g><g class="non-terminal ">
<path class="comment com1" d="M470.0 406h0.0" /><path class="comment com2" d="M529.0 406h0.0" /><text class="comment" x="499.5" y="411">Student</text></g></g><path class="oneor oom4" d="M756.0 527h10" /><path class="oneor oom5" d="M470.0 457a10 10 0 0 0 -10 10v85a10 10 0 0 0 10 10" /><g>
<path class="skip" d="M470.0 562h286" /></g><path class="oneor oom6" d="M756.0 562a10 10 0 0 0 10 -10v-15a10 10 0 0 0 -10 -10" /></g><path class="choice ch6" d="M766.0 527h20" /></g><path class="seq seq3" d="M786.0 527h10" /><g>
<path class="stack stack1" d="M796.0 527h0.0" /><g class="non-terminal ">
<path class="nonterm nt1" d="M796.0 527h10.0" /><path class="nonterm nt2" d="M850.0 527h10.0" /><rect height="22" width="44" x="806" y="516"></rect><text x="828" y="531">\n*</text></g><path class="stack stack5" d="M860.0 527h0.0" /></g></g><g class="non-terminal ">
<path class="comment com1" d="M204.0 374h0.0" /><path class="comment com2" d="M312.0 374h0.0" /><text class="comment" x="258" y="379">field:students</text></g></g><path class="stack stack3" d="M884.0 527a10 10 0 0 1 10 10v31a10 10 0 0 1 -10 10h-694a10 10 0 0 0 -10 10v79a10 10 0 0 0 10 10h10" /><g>
<path class="group gr1" d="M190.0 677h10.0" /><path class="group gr2" d="M874.0 747h10.0" /><rect class="group-box" height="188" rx="10" ry="10" width="674" x="200" y="602"></rect><g>
<path class="seq seq1" d="M200.0 677h10.0" /><path class="seq seq2" d="M864.0 747h10.0" /><g>
<path class="stack stack1" d="M210.0 677h0.0" /><g class="terminal ">
<path class="terminal term1" d="M210.0 677h10.0" /><path class="terminal term2" d="M424.0 677h10.0" /><rect height="22" rx="10" ry="10" width="204" x="220" y="666"></rect><text x="322" y="681">Student number, Score\n</text></g><path class="stack stack5" d="M434.0 677h0.0" /></g><path class="seq seq4" d="M434.0 677h10" /><g>
<path class="choice ch1" d="M444.0 677h0.0" /><path class="choice ch2" d="M790.0 747h0.0" /><path class="choice ch3" d="M444.0 677a10 10 0 0 0 10 -10v-47a10 10 0 0 1 10 -10" /><g>
<path class="skip" d="M464.0 610h306" /></g><path class="choice ch4" d="M770.0 610a10 10 0 0 1 10 10v117a10 10 0 0 0 10 10" /><path class="choice ch5" d="M444.0 677h20" /><g>
<path class="oneor oom1" d="M464.0 677h0.0" /><path class="oneor oom2" d="M770.0 747h0.0" /><path class="oneor oom3" d="M464.0 677h10" /><g>
<path class="group gr1" d="M474.0 677h0.0" /><path class="group gr2" d="M760.0 747h0.0" /><rect class="group-box" height="140" rx="10" ry="10" width="286" x="474" y="634"></rect><g>
<path class="stack stack1" d="M474.0 677h10.0" /><path class="stack stack2" d="M484.0 677h10" /><g>
<path class="group gr1" d="M494.0 677h10.0" /><path class="group gr2" d="M730.0 677h10.0" /><rect class="group-box" height="38" rx="10" ry="10" width="226" x="504" y="658"></rect><g>
<path class="seq seq1" d="M504.0 677h10.0" /><path class="seq seq2" d="M720.0 677h10.0" /><g>
<path class="seq seq1" d="M514.0 677h0.0" /><path class="seq seq2" d="M644.0 677h0.0" /><g class="non-terminal ">
<path class="nonterm nt1" d="M514.0 677h0.0" /><path class="nonterm nt2" d="M558.0 677h0.0" /><rect height="22" width="44" x="514" y="666"></rect><text x="536" y="681">\d+</text></g><path class="seq seq4" d="M558.0 677h10" /><path class="seq seq3" d="M568.0 677h10" /><g class="non-terminal ">
<path class="comment com1" d="M578.0 677h0.0" /><path class="comment com2" d="M644.0 677h0.0" /><text class="comment" x="611" y="682">map: int</text></g></g><path class="seq seq4" d="M644.0 677h10" /><path class="seq seq3" d="M654.0 677h10" /><g>
<path class="stack stack1" d="M664.0 677h0.0" /><g class="terminal ">
<path class="terminal term1" d="M664.0 677h10.0" /><path class="terminal term2" d="M710.0 677h10.0" /><rect height="22" rx="10" ry="10" width="36" x="674" y="666"></rect><text x="692" y="681">, </text></g><path class="stack stack5" d="M720.0 677h0.0" /></g></g><g class="non-terminal ">
<path class="comment com1" d="M504.0 650h0.0" /><path class="comment com2" d="M598.0 650h0.0" /><text class="comment" x="551" y="655">field:number</text></g></g><path class="stack stack3" d="M740.0 677a10 10 0 0 1 10 10v7a10 10 0 0 1 -10 10h-246a10 10 0 0 0 -10 10v23a10 10 0 0 0 10 10h10" /><g>
<path class="group gr1" d="M494.0 747h10.0" /><path class="group gr2" d="M730.0 747h10.0" /><rect class="group-box" height="38" rx="10" ry="10" width="226" x="504" y="728"></rect><g>
<path class="seq seq1" d="M504.0 747h10.0" /><path class="seq seq2" d="M720.0 747h10.0" /><g>
<path class="seq seq1" d="M514.0 747h0.0" /><path class="seq seq2" d="M644.0 747h0.0" /><g class="non-terminal ">
<path class="nonterm nt1" d="M514.0 747h0.0" /><path class="nonterm nt2" d="M558.0 747h0.0" /><rect height="22" width="44" x="514" y="736"></rect><text x="536" y="751">\d+</text></g><path class="seq seq4" d="M558.0 747h10" /><path class="seq seq3" d="M568.0 747h10" /><g class="non-terminal ">
<path class="comment com1" d="M578.0 747h0.0" /><path class="comment com2" d="M644.0 747h0.0" /><text class="comment" x="611" y="752">map: int</text></g></g><path class="seq seq4" d="M644.0 747h10" /><path class="seq seq3" d="M654.0 747h10" /><g>
<path class="stack stack1" d="M664.0 747h0.0" /><g class="terminal ">
<path class="terminal term1" d="M664.0 747h10.0" /><path class="terminal term2" d="M710.0 747h10.0" /><rect height="22" rx="10" ry="10" width="36" x="674" y="736"></rect><text x="692" y="751">\n</text></g><path class="stack stack5" d="M720.0 747h0.0" /></g></g><g class="non-terminal ">
<path class="comment com1" d="M504.0 720h0.0" /><path class="comment com2" d="M591.0 720h0.0" /><text class="comment" x="547.5" y="725">field:score</text></g></g><path class="stack stack4" d="M740.0 747h10" /><path class="stack stack5" d="M750.0 747h10.0" /></g><g class="non-terminal ">
<path class="comment com1" d="M474.0 626h0.0" /><path class="comment com2" d="M519.0 626h0.0" /><text class="comment" x="496.5" y="631">Score</text></g></g><path class="oneor oom4" d="M760.0 747h10" /><path class="oneor oom5" d="M474.0 677a10 10 0 0 0 -10 10v85a10 10 0 0 0 10 10" /><g>
<path class="skip" d="M474.0 782h286" /></g><path class="oneor oom6" d="M760.0 782a10 10 0 0 0 10 -10v-15a10 10 0 0 0 -10 -10" /></g><path class="choice ch6" d="M770.0 747h20" /></g><path class="seq seq3" d="M790.0 747h10" /><g>
<path class="stack stack1" d="M800.0 747h0.0" /><g class="non-terminal ">
<path class="nonterm nt1" d="M800.0 747h10.0" /><path class="nonterm nt2" d="M854.0 747h10.0" /><rect height="22" width="44" x="810" y="736"></rect><text x="832" y="751">\n*</text></g><path class="stack stack5" d="M864.0 747h0.0" /></g></g><g class="non-terminal ">
<path class="comment com1" d="M200.0 594h0.0" /><path class="comment com2" d="M294.0 594h0.0" /><text class="comment" x="247" y="599">field:scores</text></g></g><path class="stack stack4" d="M884.0 747h10" /><path class="stack stack5" d="M894.0 747h10.0" /></g><g class="non-terminal ">
<path class="comment com1" d="M170.0 280h0.0" /><path class="comment com2" d="M250.0 280h0.0" /><text class="comment" x="210" y="285">GradeInput</text></g></g><path class="seq seq4" d="M904.0 747h10" /><path class="seq seq3" d="M914.0 747h10" /><g class="non-terminal ">
<path class="comment com1" d="M924.0 747h0.0" /><path class="comment com2" d="M1081.0 747h0.0" /><text class="comment" x="1002.5" y="752">map: from_input_grade</text></g></g><path class="oneor oom4" d="M1081.0 747h10" /><path class="oneor oom5" d="M170.0 331a10 10 0 0 0 -10 10v455a10 10 0 0 0 10 10" /><g>
<path class="skip" d="M170.0 806h911" /></g><path class="oneor oom6" d="M1081.0 806a10 10 0 0 0 10 -10v-39a10 10 0 0 0 -10 -10" /></g><path class="choice ch6" d="M1091.0 747h20" /></g><g class="non-terminal ">
<path class="comment com1" d="M140.0 248h0.0" /><path class="comment com2" d="M234.0 248h0.0" /><text class="comment" x="187" y="253">field:grades</text></g></g><path class="stack stack4" d="M1121.0 747h10" /><path class="stack stack5" d="M1131.0 747h10.0" /></g><g class="non-terminal ">
<path class="comment com1" d="M110.0 154h0.0" /><path class="comment com2" d="M162.0 154h0.0" /><text class="comment" x="136" y="159">School</text></g></g><path class="oneor oom4" d="M1141.0 747h10" /><path class="oneor oom5" d="M110.0 205a10 10 0 0 0 -10 10v605a10 10 0 0 0 10 10" /><g>
<path class="skip" d="M110.0 830h1031" /></g><path class="oneor oom6" d="M1141.0 830a10 10 0 0 0 10 -10v-63a10 10 0 0 0 -10 -10" /></g><path class="choice ch6" d="M1151.0 747h20" /></g><g class="non-terminal ">
<path class="comment com1" d="M80.0 122h0.0" /><path class="comment com2" d="M181.0 122h0.0" /><text class="comment" x="130.5" y="127">field:schools</text></g></g><path class="stack stack4" d="M1181.0 747h10" /><path class="stack stack5" d="M1191.0 747h10.0" /></g><g class="non-terminal ">
<path class="comment com1" d="M50.0 28h0.0" /><path class="comment com2" d="M88.0 28h0.0" /><text class="comment" x="69" y="33">File</text></g></g><path d="M1201 747h10" /><path class="end" d="M 1211 747 h 20 m -10 -10 v 20 m 10 -20 v 20"></path></g><style>/* <![CDATA[ */
svg.railroad-diagram {
    background-color:hsl(30,20%,95%);
}
svg.railroad-diagram path {
    stroke-width:3;
    stroke:black;
    fill:rgba(0,0,0,0);
}
svg.railroad-diagram path.start {
    stroke-width:3;
    stroke:black;
    fill:rgba(0,0,0,1);
}
svg.railroad-diagram polygon {
    stroke-width:3;
    stroke:black;
    fill:hsl(120,100%,90%);
}
svg.railroad-diagram text {
    font:bold 14px monospace;
    text-anchor:middle;
}
svg.railroad-diagram text.label{
    text-anchor:start;
}
svg.railroad-diagram text.comment{
    font:italic 12px monospace;
}
svg.railroad-diagram rect{
    stroke-width:3;
    stroke:black;
    fill:hsl(120,100%,90%);
}
svg.railroad-diagram rect.group-box {
    stroke: gray;
    stroke-dasharray: 10 5;
    fill: none;
}
/* ]]> */
</style></svg>